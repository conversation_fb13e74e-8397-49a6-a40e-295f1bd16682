<template>
  <div class="theme-switcher">
    <a-dropdown>
      <a-button type="text" class="theme-btn">
        <template #icon>
          <BgColorsOutlined />
        </template>
        主题
      </a-button>
      
      <template #overlay>
        <a-menu @click="handleThemeChange">
          <a-menu-item key="default">
            <div class="theme-option">
              <div class="theme-preview default-theme"></div>
              <span>默认主题</span>
            </div>
          </a-menu-item>
          
          <a-menu-item key="dark">
            <div class="theme-option">
              <div class="theme-preview dark-theme"></div>
              <span>暗色主题</span>
            </div>
          </a-menu-item>
          
          <a-menu-item key="medical">
            <div class="theme-option">
              <div class="theme-preview medical-theme"></div>
              <span>医美主题</span>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import { BgColorsOutlined } from '@ant-design/icons-vue'
import { switchTheme } from '@/theme/tokens'
import { message } from 'ant-design-vue'

const handleThemeChange = ({ key }: { key: string }) => {
  switchTheme(key as 'default' | 'dark' | 'medical')
  
  const themeNames = {
    default: '默认主题',
    dark: '暗色主题',
    medical: '医美主题'
  }
  
  message.success(`已切换到${themeNames[key as keyof typeof themeNames]}`)
  
  // 刷新页面以应用新主题
  setTimeout(() => {
    window.location.reload()
  }, 500)
}
</script>

<style scoped>
.theme-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.theme-btn {
  background: var(--ant-bg-color-container);
  border: 1px solid var(--ant-border-color);
  border-radius: var(--ant-border-radius);
  box-shadow: var(--ant-box-shadow);
  color: var(--ant-text-color);
}

.theme-btn:hover {
  border-color: var(--ant-primary-color);
  color: var(--ant-primary-color);
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-preview {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid var(--ant-border-color);
}

.default-theme {
  background: linear-gradient(45deg, #1890ff, #52c41a);
}

.dark-theme {
  background: linear-gradient(45deg, #434343, #262626);
}

.medical-theme {
  background: linear-gradient(45deg, #6366f1, #8b5cf6);
}
</style>

import { createApp } from 'vue'
import Antd, { ConfigProvider } from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './style.css'
import App from './App.vue'
import router, { setupRouter } from './router'
import { getCurrentTheme, applyThemeToRoot } from './theme/tokens'

// 获取当前主题配置
const currentTheme = getCurrentTheme()

// 应用主题到根元素
applyThemeToRoot(currentTheme)

// Ant Design Vue 主题配置
const themeConfig = {
  token: {
    // 主色调
    colorPrimary: currentTheme.colorPrimary,
    colorSuccess: currentTheme.colorSuccess,
    colorWarning: currentTheme.colorWarning,
    colorError: currentTheme.colorError,
    colorInfo: currentTheme.colorInfo,

    // 文字颜色
    colorText: currentTheme.colorText,
    colorTextSecondary: currentTheme.colorTextSecondary,
    colorTextTertiary: currentTheme.colorTextTertiary,
    colorTextQuaternary: currentTheme.colorTextQuaternary,

    // 背景颜色
    colorBgBase: currentTheme.colorBgBase,
    colorBgContainer: currentTheme.colorBgContainer,
    colorBgElevated: currentTheme.colorBgElevated,
    colorBgLayout: currentTheme.colorBgLayout,
    colorBgSpotlight: currentTheme.colorBgSpotlight,

    // 边框颜色
    colorBorder: currentTheme.colorBorder,
    colorBorderSecondary: currentTheme.colorBorderSecondary,

    // 圆角
    borderRadius: currentTheme.borderRadius,
    borderRadiusLG: currentTheme.borderRadiusLG,
    borderRadiusXS: currentTheme.borderRadiusXS,

    // 字体
    fontSize: currentTheme.fontSize,
    fontSizeLG: currentTheme.fontSizeLG,
    fontSizeXL: currentTheme.fontSizeXL,
    fontSizeHeading1: currentTheme.fontSizeHeading1,
    fontSizeHeading2: currentTheme.fontSizeHeading2,
    fontSizeHeading3: currentTheme.fontSizeHeading3,
    fontSizeHeading4: currentTheme.fontSizeHeading4,
    fontSizeHeading5: currentTheme.fontSizeHeading5,

    // 间距
    padding: currentTheme.padding,
    paddingLG: currentTheme.paddingLG,
    paddingXL: currentTheme.paddingXL,
    paddingXS: currentTheme.paddingXS,
    paddingXXS: currentTheme.paddingXXS,

    // 控件高度
    controlHeight: currentTheme.controlHeight,
    controlHeightLG: currentTheme.controlHeightLG,
    controlHeightSM: currentTheme.controlHeightSM,
  },
  components: {
    Button: {
      primaryShadow: '0 2px 0 rgba(5, 145, 255, 0.1)',
      defaultShadow: '0 2px 0 rgba(0, 0, 0, 0.02)',
    },
    Input: {
      activeBorderColor: currentTheme.colorPrimary,
      hoverBorderColor: currentTheme.colorPrimary,
    },
    Card: {
      boxShadowTertiary: currentTheme.boxShadowTertiary,
    }
  }
}

const app = createApp(App)

// 安装Ant Design Vue
app.use(Antd)

// 配置全局主题
app.provide('antdTheme', themeConfig)

// 安装路由
setupRouter(app)

app.mount('#app')

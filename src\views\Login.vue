<template>
  <a-layout class="login-layout">
    <!-- 左侧Logo区域 -->
    <a-layout-sider 
      :width="'50%'" 
      class="login-left"
      :trigger="null"
      :collapsible="false"
    >
      <div class="logo-container">
        <div class="logo-content">
          <div class="logo-icon">
            <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="80" height="80" rx="16" fill="#1890ff"/>
              <path d="M20 40L35 55L60 25" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h1 class="logo-title">医美CRM管理系统</h1>
          <p class="logo-subtitle">专业的医美机构客户管理平台</p>
        </div>
      </div>
    </a-layout-sider>

    <!-- 右侧登录表单区域 -->
    <a-layout-content class="login-right">
      <div class="login-form-container">
        <div class="login-form-wrapper">
          <div class="login-header">
            <h2>欢迎登录</h2>
            <p>请选择您的登录方式</p>
          </div>

          <a-form
            :model="loginForm"
            class="login-form"
            layout="vertical"
          >
            <!-- 手机号输入框 -->
            <a-form-item 
              label="手机号码" 
              name="mobile"
              :rules="[{ required: true, message: '请输入手机号码' }]"
            >
              <a-input
                v-model:value="loginForm.mobile"
                placeholder="请输入手机号码"
                size="large"
                :prefix="h(PhoneOutlined)"
              />
            </a-form-item>

            <!-- 验证码输入框 -->
            <a-form-item 
              label="验证码" 
              name="code"
              :rules="[{ required: true, message: '请输入验证码' }]"
            >
              <a-input
                v-model:value="loginForm.code"
                placeholder="请输入验证码"
                size="large"
                :prefix="h(SafetyOutlined)"
              />
            </a-form-item>

            <!-- 登录按钮组 -->
            <div class="login-buttons">
              <a-button 
                type="primary" 
                size="large" 
                block
                class="login-btn"
                @click="handleMobileLogin"
              >
                手机登录
              </a-button>
              
              <a-button 
                size="large" 
                block
                class="wechat-btn"
                @click="handleWechatLogin"
              >
                <template #icon>
                  <WechatOutlined />
                </template>
                微信登录
              </a-button>

              <a-button 
                size="large" 
                block
                class="admin-btn"
                @click="handleAdminLogin"
              >
                管理员登录
              </a-button>
            </div>
          </a-form>
        </div>
      </div>
    </a-layout-content>

    <!-- 微信登录模态框 -->
    <a-modal
      v-model:open="wechatModalVisible"
      title="微信扫码登录"
      :footer="null"
      :width="400"
      centered
    >
      <div class="wechat-qr-container">
        <div class="qr-code-placeholder">
          <div class="qr-code-box">
            <WechatOutlined style="font-size: 48px; color: #52c41a;" />
            <p>请使用微信扫描二维码</p>
            <p class="qr-tip">扫码后在手机上确认登录</p>
          </div>
        </div>
      </div>
    </a-modal>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  PhoneOutlined,
  SafetyOutlined,
  WechatOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ROUTE_PATHS } from '@/constants/routes'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loginForm = ref({
  mobile: '',
  code: ''
})

const wechatModalVisible = ref(false)

// 登录方法
const handleMobileLogin = () => {
  if (!loginForm.value.mobile) {
    message.warning('请输入手机号码')
    return
  }
  if (!loginForm.value.code) {
    message.warning('请输入验证码')
    return
  }

  // 模拟登录成功
  localStorage.setItem('access_token', 'mock_token_' + Date.now())
  localStorage.setItem('user_role', 'user')
  localStorage.setItem('user_permissions', JSON.stringify(['read', 'write']))

  message.success('登录成功！')

  // 获取重定向路径，默认跳转到工作台
  const redirect = (route.query.redirect as string) || ROUTE_PATHS.DASHBOARD
  router.push(redirect)
}

const handleWechatLogin = () => {
  wechatModalVisible.value = true
}

const handleAdminLogin = () => {
  // 模拟管理员登录成功
  localStorage.setItem('access_token', 'admin_token_' + Date.now())
  localStorage.setItem('user_role', 'admin')
  localStorage.setItem('user_permissions', JSON.stringify(['read', 'write', 'admin']))

  message.success('管理员登录成功！')

  // 获取重定向路径，默认跳转到工作台
  const redirect = (route.query.redirect as string) || ROUTE_PATHS.DASHBOARD
  router.push(redirect)
}
</script>

<style scoped>
.login-layout {
  height: 100vh;
  width: 100vw;
}

.login-left {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-container {
  text-align: center;
  color: white;
}

.logo-content {
  padding: 40px;
}

.logo-icon {
  margin-bottom: 24px;
}

.logo-title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 12px;
  color: white;
}

.logo-subtitle {
  font-size: 16px;
  opacity: 0.9;
  color: white;
}

.login-right {
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
  padding: 40px;
}

.login-form-wrapper {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #262626;
}

.login-header p {
  color: #8c8c8c;
  font-size: 14px;
}

.login-form {
  margin-top: 24px;
}

.login-buttons {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.login-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.wechat-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: #52c41a;
  border-color: #52c41a;
  color: white;
}

.wechat-btn:hover {
  background: #73d13d !important;
  border-color: #73d13d !important;
  color: white !important;
}

.admin-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: #722ed1;
  border-color: #722ed1;
  color: white;
}

.admin-btn:hover {
  background: #9254de !important;
  border-color: #9254de !important;
  color: white !important;
}

.wechat-qr-container {
  text-align: center;
  padding: 20px;
}

.qr-code-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.qr-code-box {
  text-align: center;
}

.qr-code-box p {
  margin: 12px 0 4px 0;
  font-size: 16px;
  color: #262626;
}

.qr-tip {
  font-size: 14px !important;
  color: #8c8c8c !important;
}
</style>

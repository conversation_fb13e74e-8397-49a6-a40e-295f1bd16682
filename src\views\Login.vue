<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card">
        <!-- 登录标题 -->
        <div class="login-header">
          <h2>欢迎回来</h2>
          <p>输入您的凭据以访问您的账户</p>
        </div>

        <!-- 登录表单 -->
        <a-form
          ref="formRef"
          :model="loginForm"
          class="login-form"
          layout="vertical"
          @finish="handleLogin"
        >
          <!-- 用户名登录模式 -->
          <template v-if="loginMode === 'username'">
            <!-- 用户名输入框 -->
            <a-form-item
              label="用户名"
              name="username"
              :rules="[
                { required: true, message: '请输入您的用户名' },
                { type: 'username', message: '请输入有效的用户名' }
              ]"
            >
              <a-input
                v-model:value="loginForm.username"
                placeholder="请输入您的用户名"
                size="large"
              />
            </a-form-item>

            <!-- 密码输入框 -->
            <a-form-item
              label="密码"
              name="password"
              :rules="[{ required: true, message: '请输入您的密码' }]"
            >
              <template #label>
                <div class="password-label">
                  <span>密码</span>
                  <a-button type="link" class="forgot-link" @click="handleForgotPassword">
                    忘记密码？
                  </a-button>
                </div>
              </template>
              <a-input-password
                v-model:value="loginForm.password"
                placeholder="请输入您的密码"
                size="large"
              />
            </a-form-item>
          </template>

          <!-- 验证码登录模式 -->
          <template v-else-if="loginMode === 'sms'">
            <!-- 手机号输入框 -->
            <a-form-item
              label="手机号"
              name="mobile"
              :rules="[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
              ]"
            >
              <a-input
                v-model:value="loginForm.mobile"
                placeholder="请输入手机号"
                size="large"
              />
            </a-form-item>

            <!-- 验证码输入框 -->
            <a-form-item
              label="验证码"
              name="smsCode"
              :rules="[{ required: true, message: '请输入验证码' }]"
            >
              <a-input-group compact>
                <a-input
                  v-model:value="loginForm.smsCode"
                  placeholder="请输入验证码"
                  size="large"
                  style="width: calc(100% - 120px)"
                />
                <a-button
                  size="large"
                  :disabled="countdown > 0"
                  @click="handleGetSmsCode"
                  style="width: 120px"
                >
                  {{ countdown > 0 ? `重新获取(${countdown}s)` : '获取验证码' }}
                </a-button>
              </a-input-group>
            </a-form-item>
          </template>

          <!-- 登录按钮 -->
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              block
              class="login-btn"
              :loading="loading"
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 分割线 -->
        <a-divider>其他方式登录</a-divider>

        <!-- 登录方式切换按钮 -->
        <div class="login-mode-buttons">
          <a-button
            class="mode-btn"
            size="large"
            :type="loginMode === 'sms' ? 'primary' : 'default'"
            @click="handleSwitchToSms"
          >
            <MobileOutlined />
            验证码登录
          </a-button>

          <a-button
            class="mode-btn"
            size="large"
            @click="handleWechatLogin"
          >
            <WechatOutlined />
            微信登录
          </a-button>
        </div>

        <!-- 注册链接 -->
        <div class="register-link">
          没有账号？
          <a-button type="link" @click="handleSignUp">
            注册
          </a-button>
        </div>
      </div>
    </div>

    <!-- 底部条款 -->
    <div class="footer-terms">
      By clicking continue, you agree to our
      <a href="#" @click.prevent>Terms of Service</a>
      and
      <a href="#" @click.prevent>Privacy Policy</a>.
    </div>

    <!-- 微信登录模态框 -->
    <a-modal
      v-model:open="wechatModalVisible"
      title="微信扫码登录"
      :footer="null"
      :width="400"
      centered
    >
      <div class="wechat-qr-container">
        <div class="qr-code-placeholder">
          <div class="qr-code-box">
            <div class="qr-code-icon">
              <WechatOutlined style="font-size: 64px; color: #52c41a;" />
            </div>
            <p class="qr-title">请使用微信扫描二维码登录</p>
            <p class="qr-tip">打开微信扫一扫功能扫描上方二维码</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { MobileOutlined, WechatOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ROUTE_PATHS } from '@/constants/routes'

const router = useRouter()
const route = useRoute()

// 登录模式：'username' | 'sms'
const loginMode = ref<'username' | 'sms'>('username')

// 响应式数据
const loginForm = ref({
  username: '',
  password: '',
  mobile: '',
  smsCode: ''
})

const loading = ref(false)
const formRef = ref()

// 微信登录模态框
const wechatModalVisible = ref(false)

// 验证码倒计时
const countdown = ref(0)
let countdownTimer: number | null = 0

// 登录方法
const handleLogin = async () => {
  try {
    loading.value = true

    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (loginMode.value === 'username') {
      // 用户名登录验证
      if (loginForm.value.username && loginForm.value.password) {
        const isAdmin = loginForm.value.username.includes('admin')

        localStorage.setItem('access_token', 'mock_token_' + Date.now())
        localStorage.setItem('user_role', isAdmin ? 'admin' : 'user')
        localStorage.setItem('user_permissions', JSON.stringify(
          isAdmin ? ['read', 'write', 'admin'] : ['read', 'write']
        ))

        message.success(`欢迎回来！以${isAdmin ? '管理员' : '用户'}身份登录`)

        const redirect = (route.query.redirect as string) || ROUTE_PATHS.DASHBOARD
        router.push(redirect)
      } else {
        message.error('请输入用户名和密码')
      }
    } else if (loginMode.value === 'sms') {
      // 验证码登录验证
      if (loginForm.value.mobile && loginForm.value.smsCode) {
        localStorage.setItem('access_token', 'sms_token_' + Date.now())
        localStorage.setItem('user_role', 'user')
        localStorage.setItem('user_permissions', JSON.stringify(['read', 'write']))

        message.success('验证码登录成功！')

        const redirect = (route.query.redirect as string) || ROUTE_PATHS.DASHBOARD
        router.push(redirect)
      } else {
        message.error('请输入手机号和验证码')
      }
    }
  } catch (error) {
    message.error('登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 忘记密码
const handleForgotPassword = () => {
  message.info('密码重置功能开发中...')
}

// 切换到验证码登录
const handleSwitchToSms = () => {
  if (loginMode.value === 'sms') {
    // 如果已经是验证码模式，切换回邮箱模式
    loginMode.value = 'username'
    // 清空表单
    loginForm.value.mobile = ''
    loginForm.value.smsCode = ''
    // 清除倒计时
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
    countdown.value = 0
  } else {
    // 切换到验证码模式
    loginMode.value = 'sms'
    // 清空表单
    loginForm.value.username = ''
    loginForm.value.password = ''
  }

  // 清除表单验证
  formRef.value?.clearValidate()
}

// 微信登录
const handleWechatLogin = () => {
  wechatModalVisible.value = true
}

// 获取短信验证码
const handleGetSmsCode = () => {
  if (!loginForm.value.mobile) {
    message.warning('请先输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.value.mobile)) {
    message.warning('请输入正确的手机号')
    return
  }

  // 开始倒计时
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
    }
  }, 1000)

  message.success('验证码已发送，请注意查收')
}

// 注册
const handleSignUp = () => {
  message.info('注册功能开发中...')
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer as number)
  }
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  margin-bottom: 24px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 48px 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1a1a1a;
  line-height: 1.2;
}

.login-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 24px;
}

.password-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.forgot-link {
  padding: 0;
  height: auto;
  font-size: 14px;
  color: #666;
}

.forgot-link:hover {
  color: #1890ff;
}

.login-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: #4285f4;
  border-color: #4285f4;
  border-radius: 8px;
}

.login-btn:hover {
  background: #3367d6 !important;
  border-color: #3367d6 !important;
}

.divider {
  text-align: center;
  margin: 24px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e8e8e8;
}

.divider span {
  background: white;
  padding: 0 16px;
  color: #666;
  font-size: 14px;
}

.login-mode-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.mode-btn {
  flex: 1;
  height: 48px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  background: white;
}

.mode-btn:hover {
  border-color: #d9d9d9;
  color: #1890ff;
}

.mode-btn.ant-btn-primary {
  background: #4285f4;
  border-color: #4285f4;
  color: white;
}

.mode-btn.ant-btn-primary:hover {
  background: #3367d6 !important;
  border-color: #3367d6 !important;
  color: white !important;
}

.register-link {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.register-link .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 14px;
  color: #1890ff;
  text-decoration: underline;
}

.footer-terms {
  text-align: center;
  color: #999;
  font-size: 12px;
  max-width: 400px;
  line-height: 1.5;
}

.footer-terms a {
  color: #1890ff;
  text-decoration: underline;
}

.footer-terms a:hover {
  color: #40a9ff;
}

/* 微信登录模态框样式 */
.wechat-qr-container {
  text-align: center;
  padding: 24px;
}

.qr-code-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 280px;
}

.qr-code-box {
  text-align: center;
}

.qr-code-icon {
  margin-bottom: 24px;
  padding: 24px;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background: #fafafa;
}

.qr-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 16px 0 8px 0;
}

.qr-tip {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

/* 验证码输入组合样式 */
.ant-input-group {
  display: flex;
}

.ant-input-group .ant-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.ant-input-group .ant-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 32px 24px;
    margin: 0 16px;
  }

  .login-header h2 {
    font-size: 24px;
  }

  .login-mode-buttons {
    flex-direction: column;
  }

  .mode-btn {
    width: 100%;
  }

  .ant-input-group .ant-btn {
    width: 100px;
    font-size: 12px;
  }
}
</style>

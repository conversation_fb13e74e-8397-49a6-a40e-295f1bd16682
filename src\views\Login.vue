<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card">
        <!-- 登录标题 -->
        <div class="login-header">
          <h2>欢迎回来</h2>
          <p>输入您的凭据以访问您的账户</p>
        </div>

        <!-- 登录表单 -->
        <a-form
          ref="formRef"
          :model="loginForm"
          class="login-form"
          layout="vertical"
          @finish="handleLogin"
        >
          <!-- 邮箱输入框 -->
          <a-form-item
            label="用户名"
            name="email"
            :rules="[
              { required: true, message: '请输入您的用户名' },
              { type: 'email', message: '请输入有效的用户名' }
            ]"
          >
            <a-input
              v-model:value="loginForm.email"
              placeholder="请输入您的用户名"
              size="large"
            />
          </a-form-item>

          <!-- 密码输入框 -->
          <a-form-item
            label="密码"
            name="password"
            :rules="[{ required: true, message: '请输入您的密码' }]"
          >
            <template #label>
              <div class="password-label">
                <span>密码</span>
                <a-button type="link" class="forgot-link" @click="handleForgotPassword">
                  忘记密码？
                </a-button>
              </div>
            </template>
            <a-input-password
              v-model:value="loginForm.password"
              placeholder="请输入您的密码"
              size="large"
            />
          </a-form-item>

          <!-- 登录按钮 -->
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              block
              class="login-btn"
              :loading="loading"
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 分割线 -->
        <div class="divider">
          <span>或使用以下方式登录</span>
        </div>

        <!-- 第三方登录按钮 -->
        <div class="social-login">
          <a-button
            class="social-btn"
            size="large"
            @click="handleAppleLogin"
          >
            <AppleOutlined />
          </a-button>

          <a-button
            class="social-btn"
            size="large"
            @click="handleGoogleLogin"
          >
            <GoogleOutlined />
          </a-button>

          <a-button
            class="social-btn"
            size="large"
            @click="handleMetaLogin"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
          </a-button>
        </div>

        <!-- 注册链接 -->
        <div class="register-link">
          Don't have an account?
          <a-button type="link" @click="handleSignUp">
            Sign up
          </a-button>
        </div>
      </div>
    </div>

    <!-- 底部条款 -->
    <div class="footer-terms">
      By clicking continue, you agree to our
      <a href="#" @click.prevent>Terms of Service</a>
      and
      <a href="#" @click.prevent>Privacy Policy</a>.
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { AppleOutlined, GoogleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ROUTE_PATHS } from '@/constants/routes'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loginForm = ref({
  email: '',
  password: ''
})

const loading = ref(false)
const formRef = ref()

// 登录方法
const handleLogin = async () => {
  try {
    loading.value = true

    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 简单的演示验证 - 任何邮箱和密码都可以登录
    if (loginForm.value.email && loginForm.value.password) {
      // 根据邮箱判断用户角色
      const isAdmin = loginForm.value.email.includes('admin')

      // 模拟登录成功
      localStorage.setItem('access_token', 'mock_token_' + Date.now())
      localStorage.setItem('user_role', isAdmin ? 'admin' : 'user')
      localStorage.setItem('user_permissions', JSON.stringify(
        isAdmin ? ['read', 'write', 'admin'] : ['read', 'write']
      ))

      message.success(`Welcome back! Logged in as ${isAdmin ? 'Admin' : 'User'}`)

      // 获取重定向路径，默认跳转到工作台
      const redirect = (route.query.redirect as string) || ROUTE_PATHS.DASHBOARD
      router.push(redirect)
    } else {
      message.error('Please enter both email and password')
    }
  } catch (error) {
    message.error('Login failed. Please try again.')
  } finally {
    loading.value = false
  }
}

// 忘记密码
const handleForgotPassword = () => {
  message.info('Password reset feature coming soon...')
}

// 第三方登录
const handleAppleLogin = () => {
  message.info('Apple login feature coming soon...')
}

const handleGoogleLogin = () => {
  message.info('Google login feature coming soon...')
}

const handleMetaLogin = () => {
  message.info('Meta login feature coming soon...')
}

// 注册
const handleSignUp = () => {
  message.info('Sign up feature coming soon...')
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  margin-bottom: 24px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 48px 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1a1a1a;
  line-height: 1.2;
}

.login-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 24px;
}

.password-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.forgot-link {
  padding: 0;
  height: auto;
  font-size: 14px;
  color: #666;
}

.forgot-link:hover {
  color: #1890ff;
}

.login-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: #4285f4;
  border-color: #4285f4;
  border-radius: 8px;
}

.login-btn:hover {
  background: #3367d6 !important;
  border-color: #3367d6 !important;
}

.divider {
  text-align: center;
  margin: 24px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e8e8e8;
}

.divider span {
  background: white;
  padding: 0 16px;
  color: #666;
  font-size: 14px;
}

.social-login {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.social-btn {
  flex: 1;
  height: 48px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #666;
  background: white;
}

.social-btn:hover {
  border-color: #d9d9d9;
  color: #1890ff;
}

.register-link {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.register-link .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 14px;
  color: #1890ff;
  text-decoration: underline;
}

.footer-terms {
  text-align: center;
  color: #999;
  font-size: 12px;
  max-width: 400px;
  line-height: 1.5;
}

.footer-terms a {
  color: #1890ff;
  text-decoration: underline;
}

.footer-terms a:hover {
  color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 32px 24px;
    margin: 0 16px;
  }

  .login-header h2 {
    font-size: 24px;
  }
}
</style>

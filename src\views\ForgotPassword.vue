<template>
  <div class="forgot-password-page">
    <a-result
      status="info"
      title="忘记密码"
      sub-title="密码重置功能开发中，敬请期待。"
    >
      <template #extra>
        <a-button type="primary" @click="goLogin">
          返回登录
        </a-button>
      </template>
    </a-result>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ROUTE_PATHS } from '@/constants/routes'

const router = useRouter()

const goLogin = () => {
  router.push(ROUTE_PATHS.LOGIN)
}
</script>

<style scoped>
.forgot-password-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f0f2f5;
}
</style>

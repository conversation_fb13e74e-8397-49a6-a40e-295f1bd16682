/**
 * Ant Design Vue 主题 Token 配置
 * 统一管理所有设计令牌，确保样式一致性
 */

export interface ThemeTokens {
  // 主色调
  colorPrimary: string
  colorSuccess: string
  colorWarning: string
  colorError: string
  colorInfo: string
  
  // 文字颜色
  colorText: string
  colorTextSecondary: string
  colorTextTertiary: string
  colorTextQuaternary: string
  
  // 背景颜色
  colorBgBase: string
  colorBgContainer: string
  colorBgElevated: string
  colorBgLayout: string
  colorBgSpotlight: string
  
  // 边框颜色
  colorBorder: string
  colorBorderSecondary: string
  
  // 品牌渐变色（自定义）
  colorBrandGradientStart: string
  colorBrandGradientEnd: string
  
  // 圆角
  borderRadius: number
  borderRadiusLG: number
  borderRadiusXS: number
  
  // 阴影
  boxShadow: string
  boxShadowSecondary: string
  boxShadowTertiary: string
  
  // 字体大小
  fontSize: number
  fontSizeLG: number
  fontSizeXL: number
  fontSizeHeading1: number
  fontSizeHeading2: number
  fontSizeHeading3: number
  fontSizeHeading4: number
  fontSizeHeading5: number
  
  // 间距
  padding: number
  paddingLG: number
  paddingXL: number
  paddingXS: number
  paddingXXS: number
  
  // 控件高度
  controlHeight: number
  controlHeightLG: number
  controlHeightSM: number
}

// 默认主题配置
export const defaultTheme: ThemeTokens = {
  // 主色调
  colorPrimary: '#1890ff',
  colorSuccess: '#52c41a',
  colorWarning: '#faad14',
  colorError: '#ff4d4f',
  colorInfo: '#1890ff',
  
  // 文字颜色
  colorText: '#262626',
  colorTextSecondary: '#8c8c8c',
  colorTextTertiary: '#bfbfbf',
  colorTextQuaternary: '#f0f0f0',
  
  // 背景颜色
  colorBgBase: '#ffffff',
  colorBgContainer: '#ffffff',
  colorBgElevated: '#ffffff',
  colorBgLayout: '#f5f5f5',
  colorBgSpotlight: '#ffffff',
  
  // 边框颜色
  colorBorder: '#d9d9d9',
  colorBorderSecondary: '#f0f0f0',
  
  // 品牌渐变色
  colorBrandGradientStart: '#667eea',
  colorBrandGradientEnd: '#764ba2',
  
  // 圆角
  borderRadius: 8,
  borderRadiusLG: 12,
  borderRadiusXS: 4,
  
  // 阴影
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
  boxShadowSecondary: '0 4px 12px rgba(0, 0, 0, 0.1)',
  boxShadowTertiary: '0 8px 32px rgba(0, 0, 0, 0.1)',
  
  // 字体大小
  fontSize: 14,
  fontSizeLG: 16,
  fontSizeXL: 20,
  fontSizeHeading1: 38,
  fontSizeHeading2: 30,
  fontSizeHeading3: 24,
  fontSizeHeading4: 20,
  fontSizeHeading5: 16,
  
  // 间距
  padding: 16,
  paddingLG: 24,
  paddingXL: 32,
  paddingXS: 8,
  paddingXXS: 4,
  
  // 控件高度
  controlHeight: 32,
  controlHeightLG: 40,
  controlHeightSM: 24,
}

// 暗色主题配置
export const darkTheme: ThemeTokens = {
  ...defaultTheme,
  
  // 文字颜色
  colorText: '#ffffff',
  colorTextSecondary: '#a6a6a6',
  colorTextTertiary: '#737373',
  colorTextQuaternary: '#525252',
  
  // 背景颜色
  colorBgBase: '#000000',
  colorBgContainer: '#141414',
  colorBgElevated: '#1f1f1f',
  colorBgLayout: '#000000',
  colorBgSpotlight: '#262626',
  
  // 边框颜色
  colorBorder: '#434343',
  colorBorderSecondary: '#303030',
}

// 医美品牌主题配置
export const medicalTheme: ThemeTokens = {
  ...defaultTheme,
  
  // 主色调 - 医美行业常用的蓝紫色系
  colorPrimary: '#6366f1',
  colorInfo: '#6366f1',
  
  // 品牌渐变色 - 更符合医美行业的色彩
  colorBrandGradientStart: '#6366f1',
  colorBrandGradientEnd: '#8b5cf6',
}

// CSS 变量生成器
export function generateCSSVariables(theme: ThemeTokens): Record<string, string> {
  return {
    '--ant-primary-color': theme.colorPrimary,
    '--ant-success-color': theme.colorSuccess,
    '--ant-warning-color': theme.colorWarning,
    '--ant-error-color': theme.colorError,
    '--ant-info-color': theme.colorInfo,
    
    '--ant-text-color': theme.colorText,
    '--ant-text-color-secondary': theme.colorTextSecondary,
    '--ant-text-color-tertiary': theme.colorTextTertiary,
    '--ant-text-color-quaternary': theme.colorTextQuaternary,
    
    '--ant-bg-color-base': theme.colorBgBase,
    '--ant-bg-color-container': theme.colorBgContainer,
    '--ant-bg-color-elevated': theme.colorBgElevated,
    '--ant-bg-color-layout': theme.colorBgLayout,
    '--ant-bg-color-spotlight': theme.colorBgSpotlight,
    
    '--ant-border-color': theme.colorBorder,
    '--ant-border-color-secondary': theme.colorBorderSecondary,
    
    '--ant-brand-gradient-start': theme.colorBrandGradientStart,
    '--ant-brand-gradient-end': theme.colorBrandGradientEnd,
    
    '--ant-border-radius': `${theme.borderRadius}px`,
    '--ant-border-radius-lg': `${theme.borderRadiusLG}px`,
    '--ant-border-radius-xs': `${theme.borderRadiusXS}px`,
    
    '--ant-box-shadow': theme.boxShadow,
    '--ant-box-shadow-secondary': theme.boxShadowSecondary,
    '--ant-box-shadow-tertiary': theme.boxShadowTertiary,
    
    '--ant-font-size': `${theme.fontSize}px`,
    '--ant-font-size-lg': `${theme.fontSizeLG}px`,
    '--ant-font-size-xl': `${theme.fontSizeXL}px`,
    '--ant-font-size-heading1': `${theme.fontSizeHeading1}px`,
    '--ant-font-size-heading2': `${theme.fontSizeHeading2}px`,
    '--ant-font-size-heading3': `${theme.fontSizeHeading3}px`,
    '--ant-font-size-heading4': `${theme.fontSizeHeading4}px`,
    '--ant-font-size-heading5': `${theme.fontSizeHeading5}px`,
    
    '--ant-padding': `${theme.padding}px`,
    '--ant-padding-lg': `${theme.paddingLG}px`,
    '--ant-padding-xl': `${theme.paddingXL}px`,
    '--ant-padding-xs': `${theme.paddingXS}px`,
    '--ant-padding-xxs': `${theme.paddingXXS}px`,
    
    '--ant-control-height': `${theme.controlHeight}px`,
    '--ant-control-height-lg': `${theme.controlHeightLG}px`,
    '--ant-control-height-sm': `${theme.controlHeightSM}px`,
  }
}

// 应用主题到根元素
export function applyThemeToRoot(theme: ThemeTokens): void {
  const root = document.documentElement
  const cssVariables = generateCSSVariables(theme)
  
  Object.entries(cssVariables).forEach(([property, value]) => {
    root.style.setProperty(property, value)
  })
}

// 获取当前主题
export function getCurrentTheme(): ThemeTokens {
  // 这里可以从 localStorage、用户设置等地方获取主题配置
  const savedTheme = localStorage.getItem('app-theme')
  
  switch (savedTheme) {
    case 'dark':
      return darkTheme
    case 'medical':
      return medicalTheme
    default:
      return defaultTheme
  }
}

// 切换主题
export function switchTheme(themeName: 'default' | 'dark' | 'medical'): void {
  let theme: ThemeTokens
  
  switch (themeName) {
    case 'dark':
      theme = darkTheme
      break
    case 'medical':
      theme = medicalTheme
      break
    default:
      theme = defaultTheme
  }
  
  applyThemeToRoot(theme)
  localStorage.setItem('app-theme', themeName)
}

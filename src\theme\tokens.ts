/**
 * Ant Design Vue 主题系统配置
 * 基于官方主题系统，提供主题切换和定制能力
 */

import { theme } from 'ant-design-vue'
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context'

// 主题类型定义
export type ThemeType = 'default' | 'dark' | 'compact' | 'medical'

// 主题算法映射
const themeAlgorithms = {
  default: theme.defaultAlgorithm,
  dark: theme.darkAlgorithm,
  compact: theme.compactAlgorithm,
  medical: theme.defaultAlgorithm, // 医美主题基于默认算法，通过 token 定制
}

// 医美主题的自定义 token
const medicalThemeTokens = {
  colorPrimary: '#6366f1',
  colorInfo: '#6366f1',
  colorSuccess: '#10b981',
  colorWarning: '#f59e0b',
  colorError: '#ef4444',

  // 品牌色系
  colorPrimaryBg: '#f0f0ff',
  colorPrimaryBgHover: '#e6e6ff',
  colorPrimaryBorder: '#b3b3ff',
  colorPrimaryBorderHover: '#9999ff',
  colorPrimaryHover: '#7c7cf8',
  colorPrimaryActive: '#5555f5',

  // 圆角
  borderRadius: 8,
  borderRadiusLG: 12,
  borderRadiusXS: 4,

  // 字体
  fontSizeHeading1: 38,
  fontSizeHeading2: 30,
  fontSizeHeading3: 24,
  fontSizeHeading4: 20,
  fontSizeHeading5: 16,
}

// 主题配置生成器
export function getThemeConfig(themeType: ThemeType): ThemeConfig {
  const baseConfig: ThemeConfig = {
    algorithm: themeAlgorithms[themeType],
  }

  // 医美主题需要额外的 token 定制
  if (themeType === 'medical') {
    baseConfig.token = medicalThemeTokens
  }

  return baseConfig
}

// 主题状态管理
class ThemeManager {
  private currentTheme: ThemeType = 'default'
  private listeners: Array<(theme: ThemeType) => void> = []

  constructor() {
    // 从 localStorage 恢复主题设置
    const savedTheme = localStorage.getItem('app-theme') as ThemeType
    if (savedTheme && this.isValidTheme(savedTheme)) {
      this.currentTheme = savedTheme
    }
  }

  private isValidTheme(theme: string): theme is ThemeType {
    return ['default', 'dark', 'compact', 'medical'].includes(theme)
  }

  getCurrentTheme(): ThemeType {
    return this.currentTheme
  }

  getCurrentThemeConfig(): ThemeConfig {
    return getThemeConfig(this.currentTheme)
  }

  setTheme(theme: ThemeType): void {
    if (this.currentTheme !== theme) {
      this.currentTheme = theme
      localStorage.setItem('app-theme', theme)
      this.notifyListeners()
    }
  }

  subscribe(listener: (theme: ThemeType) => void): () => void {
    this.listeners.push(listener)

    // 返回取消订阅函数
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentTheme))
  }
}

// 导出单例实例
export const themeManager = new ThemeManager()

// 主题信息配置
export const themeInfo = {
  default: {
    name: '默认主题',
    description: '标准的 Ant Design 主题',
    preview: 'linear-gradient(45deg, #1890ff, #52c41a)',
  },
  dark: {
    name: '暗色主题',
    description: '适合夜间使用的深色主题',
    preview: 'linear-gradient(45deg, #434343, #262626)',
  },
  compact: {
    name: '紧凑主题',
    description: '更紧凑的布局和间距',
    preview: 'linear-gradient(45deg, #1890ff, #722ed1)',
  },
  medical: {
    name: '医美主题',
    description: '专为医美行业设计的主题',
    preview: 'linear-gradient(45deg, #6366f1, #8b5cf6)',
  },
} as const

// 工具函数：获取主题信息
export function getThemeInfo(themeType: ThemeType) {
  return themeInfo[themeType]
}

// 工具函数：获取所有可用主题
export function getAllThemes(): Array<{ key: ThemeType; info: typeof themeInfo[ThemeType] }> {
  return Object.entries(themeInfo).map(([key, info]) => ({
    key: key as ThemeType,
    info,
  }))
}

// 工具函数：切换主题（简化版）
export function switchTheme(themeType: ThemeType): void {
  themeManager.setTheme(themeType)
}

// 工具函数：获取当前主题
export function getCurrentTheme(): ThemeType {
  return themeManager.getCurrentTheme()
}

// 工具函数：获取当前主题配置
export function getCurrentThemeConfig(): ThemeConfig {
  return themeManager.getCurrentThemeConfig()
}

// 扩展接口：自定义主题 token（为未来扩展预留）
export interface CustomThemeTokens {
  // 品牌色系
  colorBrand?: string
  colorBrandHover?: string
  colorBrandActive?: string

  // 自定义渐变色
  gradientStart?: string
  gradientEnd?: string

  // 自定义圆角
  borderRadiusCustom?: number

  // 自定义间距
  spacingCustom?: number
}

// 扩展接口：创建自定义主题配置
export function createCustomThemeConfig(
  baseTheme: ThemeType = 'default',
  customTokens: CustomThemeTokens = {}
): ThemeConfig {
  const baseConfig = getThemeConfig(baseTheme)

  // 合并自定义 token
  const mergedTokens = {
    ...baseConfig.token,
    ...customTokens,
  }

  return {
    ...baseConfig,
    token: mergedTokens,
  }
}

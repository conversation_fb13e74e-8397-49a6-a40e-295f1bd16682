<template>
  <a-config-provider :theme="currentThemeConfig">
    <slot />
  </a-config-provider>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { themeManager, getCurrentThemeConfig } from '@/theme/tokens'
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context'

// 当前主题配置
const currentThemeConfig = ref<ThemeConfig>(getCurrentThemeConfig())

// 主题变化监听器
let unsubscribe: (() => void) | null = null

onMounted(() => {
  // 订阅主题变化
  unsubscribe = themeManager.subscribe(() => {
    currentThemeConfig.value = getCurrentThemeConfig()
  })
})

onUnmounted(() => {
  // 取消订阅
  if (unsubscribe) {
    unsubscribe()
  }
})
</script>
